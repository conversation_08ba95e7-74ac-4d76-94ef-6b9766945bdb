{"$schema": "https://ui.shadcn.com/schema.json", "style": "new-york", "rsc": true, "tsx": true, "tailwind": {"config": "", "css": "app/globals.css", "baseColor": "neutral", "cssVariables": true, "prefix": ""}, "aliases": {"components": "@/components", "utils": "@/lib/utils", "ui": "@/components/ui", "lib": "@/lib", "hooks": "@/hooks"}, "iconLibrary": "lucide", "registries": {"@acme": "https://registry.acme.com/{name}.json", "@internal": {"url": "https://internal.company.com/{name}.json", "@reactbits": "https://reactbits.dev/{name}.json", "@skiper-ui": "https://skiper-ui.com/{name}.json", "@kokonutui": "https://kokonutui.com/{name}.json", "@cult-ui": "https://www.cult-ui.com/{name}.json", "headers": {"Authorization": "Bearer ${REGISTRY_TOKEN}"}}}}