# Security & Performance Notes (Template)

## Security
- AuthZ boundaries (role/ownership checks)
- Input validation (Zod/Convex validators)
- Secret handling and webhook verification

## Performance
- Query/index strategy (Convex)
- Caching/avoiding overfetch
- Rendering considerations (server vs client)

## Monitoring
- Metrics to watch
- Alerts/logging

## References
- Threat model notes, perf benchmarks
