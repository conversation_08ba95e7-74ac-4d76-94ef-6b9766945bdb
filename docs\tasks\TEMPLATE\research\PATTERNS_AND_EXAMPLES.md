# Implementation Patterns & Examples (Template)

Capture reusable patterns and small, focused examples. Keep snippets minimal and reference sources.

## Next.js (App Router)
- Route structure, loading/error patterns
- Client vs server component guidance

## Convex
- Query/mutation pattern with validators
- Index usage with `withIndex()`; avoid client-side filtering

## Clerk
- Auth flows, JWT template usage, webhook verification

## UI/Design System
- Token usage (colors, spacing, radius, shadows)
- Reusable component patterns (shadcn/ui + custom)

## Testing
- Unit/integration/E2E examples

## References
- Links to official docs and internal examples
