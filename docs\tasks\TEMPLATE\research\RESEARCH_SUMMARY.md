# Research Summary (Template)

## Purpose
Summarize key findings, trade-offs, and the recommended approach in 1–2 pages.

## Executive Summary
- Problem statement:
- Primary recommendation:
- Key reasons (3 bullets):

## Context & Constraints
- Architecture touchpoints (frontend, backend/Convex, auth/Clerk, APIs):
- Constraints (time, budget, backwards-compatibility, compliance):
- Assumptions:

## Options Considered
- Option A –
- Option B –
- Option C –

## Decision (If made) / Next Steps
- Chosen path (or criteria to choose):
- Open items to validate:
- Success metrics:

## References
- Links to docs, spikes, PRs, benchmarks
